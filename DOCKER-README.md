# Mini Exam Docker Setup

This Docker Compose setup provides a complete development environment for the Mini Exam application with PostgreSQL database and pgAdmin4 for database management.

## Services Included

- **PostgreSQL Database** (port 5432)
- **pgAdmin4** (port 5050) - Web-based PostgreSQL administration
- **NestJS Backend** (port 4000) - The Mini Exam API

## Quick Start

1. **Clone the repository and navigate to the project root**

2. **Start all services:**
   ```bash
   docker-compose up -d
   ```

3. **Access the services:**
   - **Backend API**: http://localhost:4000
   - **API Documentation (Swagger)**: http://localhost:4000/api
   - **pgAdmin4**: http://localhost:5050

## pgAdmin4 Access

- **URL**: http://localhost:5050
- **Email**: <EMAIL>
- **Password**: admin123

### Connecting to PostgreSQL in pgAdmin4

1. Open pgAdmin4 in your browser
2. Click "Add New Server"
3. In the "General" tab:
   - Name: Mini Exam DB (or any name you prefer)
4. In the "Connection" tab:
   - Host name/address: `postgres`
   - Port: `5432`
   - Username: `postgres`
   - Password: `postgres`
   - Database: `mini_exam`
5. Click "Save"

## Database Information

- **Host**: postgres (within Docker network) / localhost (from host machine)
- **Port**: 5432
- **Database**: mini_exam
- **Username**: postgres
- **Password**: postgres

## Useful Commands

### Start services
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### View logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f postgres
docker-compose logs -f pgadmin
```

### Rebuild backend after code changes
```bash
docker-compose build backend
docker-compose up -d backend
```

### Access backend container shell
```bash
docker-compose exec backend sh
```

### Access PostgreSQL directly
```bash
docker-compose exec postgres psql -U postgres -d mini_exam
```

## Data Persistence

- PostgreSQL data is persisted in the `postgres_data` Docker volume
- pgAdmin4 settings are persisted in the `pgadmin_data` Docker volume

## Development Notes

- The backend service runs in development mode with hot reload
- Source code changes are automatically reflected (volume mounted)
- Database schema synchronization is enabled in development mode

## Troubleshooting

### Port conflicts
If you get port conflicts, you can change the ports in `docker-compose.yml`:
- Backend: Change `"4000:4000"` to `"YOUR_PORT:4000"`
- PostgreSQL: Change `"5432:5432"` to `"YOUR_PORT:5432"`
- pgAdmin4: Change `"5050:80"` to `"YOUR_PORT:80"`

### Database connection issues
Make sure PostgreSQL is fully started before the backend tries to connect. You can check with:
```bash
docker-compose logs postgres
```

### Reset database
To completely reset the database:
```bash
docker-compose down -v
docker-compose up -d
```

## Environment Variables

You can create a `.env` file in the project root to override default values. See `.env.example` for available options.
