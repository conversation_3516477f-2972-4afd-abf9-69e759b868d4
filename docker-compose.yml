version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: mini-exam-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: mini_exam
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - mini-exam-network

  # pgAdmin4 for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: mini-exam-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - mini-exam-network

  # NestJS Backend Application
  backend:
    build:
      context: ./mini-exam-backend
      dockerfile: Dockerfile
    container_name: mini-exam-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 4000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
      DB_DATABASE: mini_exam
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      JWT_EXPIRES_IN: 24h
    ports:
      - "4000:4000"
    volumes:
      - ./mini-exam-backend:/app
      - /app/node_modules
    depends_on:
      - postgres
    networks:
      - mini-exam-network
    command: npm run start:dev

volumes:
  postgres_data:
  pgadmin_data:

networks:
  mini-exam-network:
    driver: bridge
