import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Course, CourseGroup } from '../../types';
import apiService from '../../services/api';

const CourseList: React.FC = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [courseGroups, setCourseGroups] = useState<CourseGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [coursesData, courseGroupsData] = await Promise.all([
          apiService.getCourses(),
          apiService.getCourseGroups(),
        ]);
        setCourses(coursesData);
        setCourseGroups(courseGroupsData);
      } catch (err: any) {
        setError('خطا در بارگذاری دروس');
        console.error('Error fetching courses:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleDelete = async (id: number) => {
    if (!window.confirm('آیا از حذف این درس اطمینان دارید؟')) {
      return;
    }

    try {
      await apiService.deleteCourse(id);
      setCourses(prev => prev.filter(c => c.id !== id));
    } catch (err: any) {
      setError('خطا در حذف درس');
      console.error('Error deleting course:', err);
    }
  };

  const getCourseGroupName = (courseGroupId: number) => {
    const courseGroup = courseGroups.find(cg => cg.id === courseGroupId);
    return courseGroup?.name || 'نامشخص';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">مدیریت دروس</h1>
          <Link
            to="/admin/courses/new"
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md"
          >
            ایجاد درس جدید
          </Link>
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {courses.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-dark-300">هیچ درسی موجود نیست</p>
            <Link
              to="/admin/courses/new"
              className="mt-4 inline-block bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md"
            >
              ایجاد اولین درس
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-dark-700">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">شناسه</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">نام درس</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">گروه درسی</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">ضریب مثبت</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">ضریب منفی</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">تاریخ ایجاد</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">عملیات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-dark-700">
                {courses.map((course) => (
                  <tr key={course.id} className="hover:bg-dark-700/50">
                    <td className="px-6 py-4 text-dark-300">{course.id}</td>
                    <td className="px-6 py-4 text-white font-medium">{course.name}</td>
                    <td className="px-6 py-4 text-dark-300">
                      {getCourseGroupName(course.courseGroupId)}
                    </td>
                    <td className="px-6 py-4 text-center text-green-400">
                      {course.positiveCoefficient}
                    </td>
                    <td className="px-6 py-4 text-center text-red-400">
                      {course.negativeCoefficient}
                    </td>
                    <td className="px-6 py-4 text-dark-300">
                      {new Date(course.createdAt).toLocaleDateString('fa-IR')}
                    </td>
                    <td className="px-6 py-4 text-center">
                      <div className="flex justify-center space-x-2 space-x-reverse">
                        <Link
                          to={`/admin/courses/edit/${course.id}`}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                        >
                          ویرایش
                        </Link>
                        <button
                          onClick={() => handleDelete(course.id)}
                          className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                        >
                          حذف
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default CourseList;
