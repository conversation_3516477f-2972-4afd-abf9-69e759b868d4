import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Course, CourseGroup, CreateExamDto, ExamCourse } from '../../types';
import apiService from '../../services/api';

interface ExamFormProps {
  isEdit?: boolean;
}

const ExamForm: React.FC<ExamFormProps> = ({ isEdit = false }) => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  
  const [formData, setFormData] = useState<CreateExamDto>({
    name: '',
    courses: [],
  });
  const [courseGroups, setCourseGroups] = useState<CourseGroup[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedCourses, setSelectedCourses] = useState<ExamCourse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [courseGroupsData, coursesData] = await Promise.all([
          apiService.getCourseGroups(),
          apiService.getCourses(),
        ]);
        
        setCourseGroups(courseGroupsData);
        setCourses(coursesData);

        if (isEdit && id) {
          const examData = await apiService.getExam(parseInt(id));
          setFormData({
            name: examData.name,
            courses: examData.courses,
          });
          setSelectedCourses(examData.courses);
        }
      } catch (err: any) {
        setError('خطا در بارگذاری اطلاعات');
        console.error('Error fetching data:', err);
      }
    };

    fetchData();
  }, [isEdit, id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedCourses.length === 0) {
      setError('لطفاً حداقل یک درس انتخاب کنید');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const examData: CreateExamDto = {
        ...formData,
        courses: selectedCourses,
      };

      if (isEdit && id) {
        await apiService.updateExam(parseInt(id), examData);
      } else {
        await apiService.createExam(examData);
      }

      navigate('/admin/exams');
    } catch (err: any) {
      setError(err.response?.data?.message || 'خطا در ذخیره آزمون');
    } finally {
      setIsLoading(false);
    }
  };

  const addCourse = (courseId: number) => {
    if (selectedCourses.find(c => c.courseId === courseId)) {
      return;
    }

    setSelectedCourses(prev => [
      ...prev,
      { courseId, questionCount: 1 }
    ]);
  };

  const removeCourse = (courseId: number) => {
    setSelectedCourses(prev => prev.filter(c => c.courseId !== courseId));
  };

  const updateQuestionCount = (courseId: number, questionCount: number) => {
    setSelectedCourses(prev => prev.map(c => 
      c.courseId === courseId ? { ...c, questionCount } : c
    ));
  };

  const getCourseById = (courseId: number) => {
    return courses.find(c => c.id === courseId);
  };

  const getCourseGroupById = (courseGroupId: number) => {
    return courseGroups.find(cg => cg.id === courseGroupId);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">
            {isEdit ? 'ویرایش آزمون' : 'ایجاد آزمون جدید'}
          </h1>
          <button
            onClick={() => navigate('/admin/exams')}
            className="bg-dark-700 hover:bg-dark-600 text-white px-4 py-2 rounded-md"
          >
            بازگشت
          </button>
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-dark-200 mb-2">
              نام آزمون
            </label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-dark-600 bg-dark-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            />
          </div>

          <div>
            <h3 className="text-lg font-medium text-white mb-4">انتخاب دروس</h3>
            
            {courseGroups.map(group => (
              <div key={group.id} className="mb-6">
                <h4 className="text-md font-medium text-primary-400 mb-3">{group.name}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {courses
                    .filter(course => course.courseGroupId === group.id)
                    .map(course => (
                      <div
                        key={course.id}
                        className="flex items-center justify-between bg-dark-700 p-3 rounded-md"
                      >
                        <span className="text-white text-sm">{course.name}</span>
                        <button
                          type="button"
                          onClick={() => addCourse(course.id)}
                          disabled={selectedCourses.some(c => c.courseId === course.id)}
                          className="bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-xs"
                        >
                          {selectedCourses.some(c => c.courseId === course.id) ? 'انتخاب شده' : 'انتخاب'}
                        </button>
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>

          {selectedCourses.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-white mb-4">دروس انتخاب شده</h3>
              <div className="space-y-3">
                {selectedCourses.map(examCourse => {
                  const course = getCourseById(examCourse.courseId);
                  const courseGroup = course ? getCourseGroupById(course.courseGroupId) : null;
                  
                  return (
                    <div key={examCourse.courseId} className="flex items-center justify-between bg-dark-700 p-4 rounded-md">
                      <div>
                        <span className="text-white font-medium">{course?.name}</span>
                        <span className="text-dark-300 text-sm mr-2">({courseGroup?.name})</span>
                      </div>
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <label className="text-dark-200 text-sm">تعداد سوال:</label>
                        <input
                          type="number"
                          min="1"
                          max="100"
                          value={examCourse.questionCount}
                          onChange={(e) => updateQuestionCount(examCourse.courseId, parseInt(e.target.value) || 1)}
                          className="w-20 px-2 py-1 border border-dark-600 bg-dark-600 text-white rounded text-center focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                        <button
                          type="button"
                          onClick={() => removeCourse(examCourse.courseId)}
                          className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                        >
                          حذف
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-4 space-x-reverse">
            <button
              type="button"
              onClick={() => navigate('/admin/exams')}
              className="bg-dark-600 hover:bg-dark-500 text-white px-6 py-2 rounded-md"
            >
              انصراف
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-md"
            >
              {isLoading ? 'در حال ذخیره...' : (isEdit ? 'ویرایش' : 'ایجاد')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ExamForm;
