import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';

const Navbar: React.FC = () => {
  const { user, logout, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <nav className="bg-dark-800 border-b border-dark-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0">
              <h1 className="text-xl font-bold text-white">سیستم آزمون</h1>
            </Link>
            
            <div className="hidden md:block mr-10">
              <div className="flex items-baseline space-x-4 space-x-reverse">
                <Link
                  to="/"
                  className="text-dark-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                >
                  داشبورد
                </Link>
                
                {isAdmin() && (
                  <>
                    <Link
                      to="/admin/course-groups"
                      className="text-dark-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                    >
                      گروه‌های درسی
                    </Link>
                    <Link
                      to="/admin/courses"
                      className="text-dark-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                    >
                      دروس
                    </Link>
                    <Link
                      to="/admin/exams"
                      className="text-dark-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                    >
                      آزمون‌ها
                    </Link>
                  </>
                )}
                
                {!isAdmin() && (
                  <>
                    <Link
                      to="/exams"
                      className="text-dark-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                    >
                      آزمون‌ها
                    </Link>
                    <Link
                      to="/my-sessions"
                      className="text-dark-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                    >
                      نتایج من
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>
          
          <div className="hidden md:block">
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-dark-300 text-sm">
                {user?.username} ({user?.role === UserRole.ADMIN ? 'مدیر' : 'دانشجو'})
              </span>
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                خروج
              </button>
            </div>
          </div>
          
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-dark-400 hover:text-white hover:bg-dark-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
            >
              <svg
                className="h-6 w-6"
                stroke="currentColor"
                fill="none"
                viewBox="0 0 24 24"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {isMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-dark-800">
            <Link
              to="/"
              className="text-dark-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium"
              onClick={() => setIsMenuOpen(false)}
            >
              داشبورد
            </Link>
            
            {isAdmin() && (
              <>
                <Link
                  to="/admin/course-groups"
                  className="text-dark-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  گروه‌های درسی
                </Link>
                <Link
                  to="/admin/courses"
                  className="text-dark-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  دروس
                </Link>
                <Link
                  to="/admin/exams"
                  className="text-dark-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  آزمون‌ها
                </Link>
              </>
            )}
            
            {!isAdmin() && (
              <>
                <Link
                  to="/exams"
                  className="text-dark-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  آزمون‌ها
                </Link>
                <Link
                  to="/my-sessions"
                  className="text-dark-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  نتایج من
                </Link>
              </>
            )}
            
            <div className="border-t border-dark-700 pt-4">
              <div className="px-3 py-2">
                <span className="text-dark-300 text-sm">
                  {user?.username} ({user?.role === UserRole.ADMIN ? 'مدیر' : 'دانشجو'})
                </span>
              </div>
              <button
                onClick={handleLogout}
                className="w-full text-right bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-base font-medium"
              >
                خروج
              </button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
