@import url('https://fonts.googleapis.com/css2?family=Vazir:wght@300;400;500;600;700&display=swap');

body {
  margin: 0;
  font-family: 'Vazir', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Basic utility classes to replace Tailwind */
.min-h-screen { min-height: 100vh; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }
.bg-dark-900 { background-color: #0f172a; }
.bg-dark-800 { background-color: #1e293b; }
.bg-dark-700 { background-color: #334155; }
.bg-dark-600 { background-color: #475569; }
.text-white { color: white; }
.text-dark-300 { color: #cbd5e1; }
.text-dark-200 { color: #e2e8f0; }
.text-primary-400 { color: #38bdf8; }
.text-primary-500 { color: #0ea5e9; }
.text-red-400 { color: #f87171; }
.text-green-200 { color: #bbf7d0; }
.bg-primary-600 { background-color: #0284c7; }
.bg-primary-700 { background-color: #0369a1; }
.bg-red-600 { background-color: #dc2626; }
.bg-red-700 { background-color: #b91c1c; }
.bg-green-900 { background-color: #14532d; }
.border { border: 1px solid; }
.border-dark-600 { border-color: #475569; }
.border-dark-700 { border-color: #334155; }
.border-red-500 { border-color: #ef4444; }
.border-green-500 { border-color: #22c55e; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.p-6 { padding: 1.5rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-10 { margin-right: 2.5rem; }
.w-full { width: 100%; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.max-w-md { max-width: 28rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-reverse > * + * { margin-left: 0; margin-right: 0.75rem; }
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.hover\:bg-primary-700:hover { background-color: #0369a1; }
.hover\:bg-red-700:hover { background-color: #b91c1c; }
.hover\:bg-dark-600:hover { background-color: #475569; }
.hover\:bg-dark-700:hover { background-color: #334155; }
.hover\:text-white:hover { color: white; }
.hover\:text-primary-300:hover { color: #7dd3fc; }
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px; }
.focus\:ring-primary-500:focus { box-shadow: 0 0 0 2px #0ea5e9; }
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.cursor-not-allowed { cursor: not-allowed; }
.inline-block { display: inline-block; }
.block { display: block; }
.hidden { display: none; }
.overflow-x-auto { overflow-x: auto; }
.divide-y > * + * { border-top: 1px solid; }
.divide-dark-700 > * + * { border-color: #334155; }
.animate-spin { animation: spin 1s linear infinite; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Container and responsive utilities */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .sm\:text-sm { font-size: 0.875rem; }
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
}

@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:hidden { display: none; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}
